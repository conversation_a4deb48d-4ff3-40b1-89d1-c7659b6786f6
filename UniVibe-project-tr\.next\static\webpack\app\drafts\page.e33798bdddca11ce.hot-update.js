"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/drafts/page",{

/***/ "(app-pages-browser)/./app/drafts/page.tsx":
/*!*****************************!*\
  !*** ./app/drafts/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DraftsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_events__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/events */ \"(app-pages-browser)/./lib/events.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DraftsPage() {\n    _s();\n    const [drafts, setDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [filteredDrafts, setFilteredDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [publishingDrafts, setPublishingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [deletingDrafts, setDeletingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"All\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"updated\");\n    // Load drafts on component mount and when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            loadDrafts();\n            // Add event listener for when the page becomes visible (user returns from edit page)\n            const handleVisibilityChange = {\n                \"DraftsPage.useEffect.handleVisibilityChange\": ()=>{\n                    if (!document.hidden) {\n                        loadDrafts();\n                    }\n                }\n            }[\"DraftsPage.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            // Also listen for focus events\n            const handleFocus = {\n                \"DraftsPage.useEffect.handleFocus\": ()=>{\n                    loadDrafts();\n                }\n            }[\"DraftsPage.useEffect.handleFocus\"];\n            window.addEventListener('focus', handleFocus);\n            return ({\n                \"DraftsPage.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    window.removeEventListener('focus', handleFocus);\n                }\n            })[\"DraftsPage.useEffect\"];\n        }\n    }[\"DraftsPage.useEffect\"], []);\n    const loadDrafts = ()=>{\n        const draftData = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.getDrafts)();\n        const draftArray = Object.values(draftData);\n        // Deduplicate drafts by ID to prevent React key conflicts\n        const uniqueDrafts = draftArray.reduce((acc, draft)=>{\n            // Only keep the draft if we haven't seen this ID before\n            if (!acc.some((existingDraft)=>existingDraft.id === draft.id)) {\n                acc.push(draft);\n            }\n            return acc;\n        }, []);\n        setDrafts(uniqueDrafts);\n    };\n    // Filter and sort drafts\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            let filtered = [\n                ...drafts\n            ];\n            // Apply search filter\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.title.toLowerCase().includes(searchQuery.toLowerCase()) || draft.description.toLowerCase().includes(searchQuery.toLowerCase()) || draft.location.toLowerCase().includes(searchQuery.toLowerCase())\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply category filter\n            if (selectedCategory !== \"All\") {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.category === selectedCategory\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply sorting\n            filtered.sort({\n                \"DraftsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case \"title\":\n                            return a.title.localeCompare(b.title);\n                        case \"created\":\n                            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                        case \"category\":\n                            return a.category.localeCompare(b.category);\n                        default:\n                            return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n                    }\n                }\n            }[\"DraftsPage.useEffect\"]);\n            setFilteredDrafts(filtered);\n        }\n    }[\"DraftsPage.useEffect\"], [\n        drafts,\n        searchQuery,\n        selectedCategory,\n        sortBy\n    ]);\n    // Get unique categories\n    const categories = [\n        \"All\",\n        ...Array.from(new Set(drafts.map((draft)=>draft.category).filter(Boolean)))\n    ];\n    const handlePublishDraft = async (draftId, draftTitle)=>{\n        setPublishingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const { data, error } = await (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.publishDraft)(draftId);\n            if (error) {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to publish draft\", error);\n                return;\n            }\n            // Success!\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showEventCreatedNotification)(draftTitle, false, data === null || data === void 0 ? void 0 : data.id);\n            loadDrafts() // Refresh the drafts list\n            ;\n        } catch (error) {\n            console.error(\"Error publishing draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setPublishingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const handleDeleteDraft = async (draftId)=>{\n        setDeletingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const success = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.deleteDraft)(draftId);\n            if (success) {\n                loadDrafts() // Refresh the drafts list\n                ;\n            } else {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to delete draft\", \"Please try again\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setDeletingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"No date set\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const formatTime = (timeString)=>{\n        if (!timeString) return \"No time set\";\n        const [hours, minutes] = timeString.split(':');\n        const date = new Date();\n        date.setHours(parseInt(hours), parseInt(minutes));\n        return date.toLocaleTimeString('en-US', {\n            hour: 'numeric',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-background via-background to-primary/5\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-2xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 text-accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-bold text-white\",\n                                                        children: drafts.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold bg-gradient-to-r from-white via-white to-accent bg-clip-text text-transparent mb-2\",\n                                                    children: \"Draft Studio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground text-lg\",\n                                                    children: \"Craft, refine, and launch your events when the moment is perfect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    asChild: true,\n                                    size: \"lg\",\n                                    className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/post\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:rotate-90 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create New Event\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 opacity-70\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex flex-col sm:flex-row gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-white/10 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                            placeholder: \"Search drafts by title, description, or location...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pl-10 bg-background/50 border-white/20 focus:border-accent/50 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedCategory\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSelectedCategory(category),\n                                                    className: selectedCategory === category ? \"bg-accent/10\" : \"\",\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sort\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"updated\"),\n                                                    className: sortBy === \"updated\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Last Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"created\"),\n                                                    className: sortBy === \"created\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Date Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"title\"),\n                                                    className: sortBy === \"title\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Title A-Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"category\"),\n                                                    className: sortBy === \"category\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        drafts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        filteredDrafts.length,\n                                        \" of \",\n                                        drafts.length,\n                                        \" drafts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        'Filtered by \"',\n                                        searchQuery,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCategory !== \"All\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Category: \",\n                                        selectedCategory\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                drafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-2xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 rounded-3xl bg-gradient-to-br from-accent/20 to-primary/20 w-fit mx-auto border border-accent/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 text-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent\",\n                                children: \"Your Draft Studio Awaits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-lg mb-8 max-w-md mx-auto\",\n                                children: \"Transform your event ideas into reality. Start crafting your first event and save it as a draft to perfect every detail.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        asChild: true,\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/post\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Your First Event\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 opacity-70\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        className: \"border-white/20 hover:bg-accent/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Browse Examples\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, this) : filteredDrafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 rounded-2xl bg-muted/20 w-fit mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"No drafts match your search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: \"Try adjusting your search terms or filters to find what you're looking for\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setSelectedCategory(\"All\");\n                                },\n                                variant: \"outline\",\n                                className: \"border-white/20 hover:bg-accent/10\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8\",\n                    children: filteredDrafts.map((draft, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"group shadow-xl border-white/10 hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm hover:scale-[1.02] hover:border-accent/30\",\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-accent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs bg-accent/10 border-accent/30 text-accent\",\n                                                                        children: \"Draft\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full bg-accent animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-2xl mb-3 group-hover:text-accent transition-colors duration-300\",\n                                                        children: draft.title || \"Untitled Event\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    draft.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mb-2 bg-primary/10 text-primary border-primary/20\",\n                                                        children: draft.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        className: \"w-48\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                asChild: true,\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/drafts/edit/\".concat(draft.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Edit Draft\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2 text-destructive focus:text-destructive\",\n                                                                onClick: ()=>handleDeleteDraft(draft.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Delete Draft\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        draft.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl bg-muted/20 border border-white/10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground line-clamp-3 leading-relaxed\",\n                                                children: draft.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatDate(draft.date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-primary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatTime(draft.time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-secondary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-secondary-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Location\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: draft.location || \"No location set\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-muted-foreground bg-muted/10 rounded-lg p-3 border border-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Created \",\n                                                                new Date(draft.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated \",\n                                                                new Date(draft.updated_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>handlePublishDraft(draft.id, draft.title),\n                                                    disabled: publishingDrafts.has(draft.id),\n                                                    size: \"lg\",\n                                                    className: \"flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                                    children: publishingDrafts.has(draft.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Publishing...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"\\uD83D\\uDE80 Launch Event\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 opacity-70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-white/20 hover:bg-accent/10 hover:border-accent/30 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/drafts/edit/\".concat(draft.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, draft.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(DraftsPage, \"Rp8Sy1C6itTUHDXLMhZBa+GMjzM=\");\n_c = DraftsPage;\nvar _c;\n$RefreshReg$(_c, \"DraftsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/drafts/page.tsx\n"));

/***/ })

});