"use client"

import { FilterPills } from "@/components/filter-pills"
import { EventCard } from "@/components/event-card"
import { useState, useEffect } from "react"
import { <PERSON>rk<PERSON> } from "lucide-react"
import { getEvents } from "@/lib/events"

// Mock data
const categories = ["All", "Music", "Clubs", "Academic", "Sports", "Food", "Arts", "Other"]

const mockEvents = [
  {
    id: "1",
    title: "Spring Music Festival 2024",
    date: "March 15",
    time: "7:00 PM",
    location: "University Quad",
    organizer: "Music Society",
    attendees: 234,
    category: "Music",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "2",
    title: "Tech Talk: AI in Healthcare",
    date: "March 18",
    time: "2:00 PM",
    location: "Engineering Building, Room 101",
    organizer: "Computer Science Club",
    attendees: 89,
    category: "Academic",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "3",
    title: "Basketball Championship Finals",
    date: "March 20",
    time: "6:00 PM",
    location: "Sports Complex",
    organizer: "Athletics Department",
    attendees: 456,
    category: "Sports",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "4",
    title: "Art Gallery Opening Night",
    date: "March 22",
    time: "5:30 PM",
    location: "Student Center Gallery",
    organizer: "Art Club",
    attendees: 67,
    category: "Arts",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "5",
    title: "Food Truck Festival",
    date: "March 25",
    time: "11:00 AM",
    location: "Campus Green",
    organizer: "Student Government",
    attendees: 312,
    category: "Food",
    image: "/placeholder.svg?height=192&width=400",
  },
]

export default function HomePage() {
  const [activeCategory, setActiveCategory] = useState("All")
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")

  // Fetch events from Supabase and handle URL search params
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const { data, error } = await getEvents()
        if (error) {
          console.error('Error fetching events:', error)
          // Fallback to mock data if there's an error
          setEvents(mockEvents)
        } else {
          // Transform Supabase data to match EventCard props
          const transformedEvents = data?.map((event: any) => ({
            id: event.id,
            title: event.title,
            date: formatDate(event.date),
            time: event.time,
            location: event.location,
            organizer: event.organizer,
            attendees: event.attendees_count || 0,
            category: event.category,
            image: event.image_url || "/placeholder.svg?height=192&width=400",
          })) || []
          setEvents(transformedEvents)
        }
      } catch (error) {
        console.error('Unexpected error fetching events:', error)
        setEvents(mockEvents)
      } finally {
        setLoading(false)
      }
    }

    // Check for search query in URL
    const urlParams = new URLSearchParams(window.location.search)
    const searchParam = urlParams.get('search')
    if (searchParam) {
      setSearchQuery(searchParam)
    }

    fetchEvents()
  }, [])

  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "TBD"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric'
    })
  }

  // Filter events by category and search query
  const filteredEvents = events.filter((event) => {
    const matchesCategory = activeCategory === "All" || event.category === activeCategory
    const matchesSearch = !searchQuery ||
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.organizer.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesCategory && matchesSearch
  })

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="text-center space-y-2 py-4">
        <h2 className="text-2xl font-bold gradient-text">Discover Amazing Events</h2>
        <p className="text-muted-foreground">Find and join events happening around your campus</p>
      </div>

      {/* Filters */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-accent" />
          Categories
        </h3>
        <FilterPills categories={categories} activeCategory={activeCategory} onCategoryChange={setActiveCategory} />
      </div>

      {/* Events Grid */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            {activeCategory === "All" ? "All Events" : `${activeCategory} Events`}
          </h3>
          <span className="text-sm text-muted-foreground">
            {filteredEvents.length} event{filteredEvents.length !== 1 ? "s" : ""} found
          </span>
        </div>

        {/* Responsive Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
          {loading ? (
            // Enhanced Loading skeleton with responsive grid
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-muted rounded-xl h-80 w-full shadow-lg"></div>
              </div>
            ))
          ) : filteredEvents.length > 0 ? (
            filteredEvents.map((event, index) => (
              <div
                key={event.id}
                className="animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <EventCard {...event} />
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-16">
              <div className="p-6 rounded-2xl bg-muted/20 w-fit mx-auto mb-4">
                <Sparkles className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No events found</h3>
              <p className="text-muted-foreground">
                {activeCategory === "All"
                  ? "No events are currently available. Check back later!"
                  : `No events found in the ${activeCategory} category.`
                }
              </p>
            </div>
          )}
        </div>
      </div>

      {filteredEvents.length === 0 && (
        <div className="text-center py-16 space-y-4">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <Sparkles className="w-8 h-8 text-muted-foreground" />
          </div>
          <div>
            <p className="text-lg font-semibold text-muted-foreground">No events found</p>
            <p className="text-sm text-muted-foreground">Try selecting a different category</p>
          </div>
        </div>
      )}
    </div>
  )
}
