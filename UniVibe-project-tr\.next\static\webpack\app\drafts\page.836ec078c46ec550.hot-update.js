"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/drafts/page",{

/***/ "(app-pages-browser)/./app/drafts/page.tsx":
/*!*****************************!*\
  !*** ./app/drafts/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DraftsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_events__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/events */ \"(app-pages-browser)/./lib/events.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DraftsPage() {\n    _s();\n    const [drafts, setDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [filteredDrafts, setFilteredDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [publishingDrafts, setPublishingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [deletingDrafts, setDeletingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"All\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"updated\");\n    // Load drafts on component mount and when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            loadDrafts();\n            // Add event listener for when the page becomes visible (user returns from edit page)\n            const handleVisibilityChange = {\n                \"DraftsPage.useEffect.handleVisibilityChange\": ()=>{\n                    if (!document.hidden) {\n                        loadDrafts();\n                    }\n                }\n            }[\"DraftsPage.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            // Also listen for focus events\n            const handleFocus = {\n                \"DraftsPage.useEffect.handleFocus\": ()=>{\n                    loadDrafts();\n                }\n            }[\"DraftsPage.useEffect.handleFocus\"];\n            window.addEventListener('focus', handleFocus);\n            return ({\n                \"DraftsPage.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    window.removeEventListener('focus', handleFocus);\n                }\n            })[\"DraftsPage.useEffect\"];\n        }\n    }[\"DraftsPage.useEffect\"], []);\n    const loadDrafts = ()=>{\n        // First clean up any corrupted data\n        (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.cleanupDrafts)();\n        const draftData = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.getDrafts)();\n        const draftArray = Object.values(draftData);\n        // Deduplicate drafts by ID to prevent React key conflicts\n        const uniqueDrafts = draftArray.reduce((acc, draft)=>{\n            // Only keep the draft if we haven't seen this ID before\n            if (!acc.some((existingDraft)=>existingDraft.id === draft.id)) {\n                acc.push(draft);\n            }\n            return acc;\n        }, []);\n        // Debug logging\n        console.log('Raw draft data:', draftData);\n        console.log('Draft array:', draftArray);\n        console.log('Unique drafts:', uniqueDrafts);\n        console.log('Draft IDs:', uniqueDrafts.map((d)=>d.id));\n        setDrafts(uniqueDrafts);\n    };\n    // Filter and sort drafts\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            let filtered = [\n                ...drafts\n            ];\n            // Apply search filter\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.title.toLowerCase().includes(searchQuery.toLowerCase()) || draft.description.toLowerCase().includes(searchQuery.toLowerCase()) || draft.location.toLowerCase().includes(searchQuery.toLowerCase())\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply category filter\n            if (selectedCategory !== \"All\") {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.category === selectedCategory\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply sorting\n            filtered.sort({\n                \"DraftsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case \"title\":\n                            return a.title.localeCompare(b.title);\n                        case \"created\":\n                            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                        case \"category\":\n                            return a.category.localeCompare(b.category);\n                        default:\n                            return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n                    }\n                }\n            }[\"DraftsPage.useEffect\"]);\n            setFilteredDrafts(filtered);\n        }\n    }[\"DraftsPage.useEffect\"], [\n        drafts,\n        searchQuery,\n        selectedCategory,\n        sortBy\n    ]);\n    // Get unique categories\n    const categories = [\n        \"All\",\n        ...Array.from(new Set(drafts.map((draft)=>draft.category).filter(Boolean)))\n    ];\n    const handlePublishDraft = async (draftId, draftTitle)=>{\n        setPublishingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const { data, error } = await (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.publishDraft)(draftId);\n            if (error) {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to publish draft\", error);\n                return;\n            }\n            // Success!\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showEventCreatedNotification)(draftTitle, false, data === null || data === void 0 ? void 0 : data.id);\n            loadDrafts() // Refresh the drafts list\n            ;\n        } catch (error) {\n            console.error(\"Error publishing draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setPublishingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const handleDeleteDraft = async (draftId)=>{\n        setDeletingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const success = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.deleteDraft)(draftId);\n            if (success) {\n                loadDrafts() // Refresh the drafts list\n                ;\n            } else {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to delete draft\", \"Please try again\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setDeletingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"No date set\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const formatTime = (timeString)=>{\n        if (!timeString) return \"No time set\";\n        const [hours, minutes] = timeString.split(':');\n        const date = new Date();\n        date.setHours(parseInt(hours), parseInt(minutes));\n        return date.toLocaleTimeString('en-US', {\n            hour: 'numeric',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-background via-background to-primary/5\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-2xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 text-accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-bold text-white\",\n                                                        children: drafts.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold bg-gradient-to-r from-white via-white to-accent bg-clip-text text-transparent mb-2\",\n                                                    children: \"Draft Studio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground text-lg\",\n                                                    children: \"Craft, refine, and launch your events when the moment is perfect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    asChild: true,\n                                    size: \"lg\",\n                                    className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/post\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:rotate-90 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create New Event\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 opacity-70\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex flex-col sm:flex-row gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-white/10 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                            placeholder: \"Search drafts by title, description, or location...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pl-10 bg-background/50 border-white/20 focus:border-accent/50 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedCategory\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSelectedCategory(category),\n                                                    className: selectedCategory === category ? \"bg-accent/10\" : \"\",\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sort\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"updated\"),\n                                                    className: sortBy === \"updated\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Last Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"created\"),\n                                                    className: sortBy === \"created\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Date Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"title\"),\n                                                    className: sortBy === \"title\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Title A-Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"category\"),\n                                                    className: sortBy === \"category\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        drafts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        filteredDrafts.length,\n                                        \" of \",\n                                        drafts.length,\n                                        \" drafts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this),\n                                        'Filtered by \"',\n                                        searchQuery,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCategory !== \"All\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Category: \",\n                                        selectedCategory\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                drafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-2xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 rounded-3xl bg-gradient-to-br from-accent/20 to-primary/20 w-fit mx-auto border border-accent/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 text-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent\",\n                                children: \"Your Draft Studio Awaits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-lg mb-8 max-w-md mx-auto\",\n                                children: \"Transform your event ideas into reality. Start crafting your first event and save it as a draft to perfect every detail.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        asChild: true,\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/post\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Your First Event\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 opacity-70\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        className: \"border-white/20 hover:bg-accent/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Browse Examples\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this) : filteredDrafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 rounded-2xl bg-muted/20 w-fit mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"No drafts match your search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: \"Try adjusting your search terms or filters to find what you're looking for\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setSelectedCategory(\"All\");\n                                },\n                                variant: \"outline\",\n                                className: \"border-white/20 hover:bg-accent/10\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8\",\n                    children: filteredDrafts.map((draft, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"group shadow-xl border-white/10 hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm hover:scale-[1.02] hover:border-accent/30\",\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-accent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs bg-accent/10 border-accent/30 text-accent\",\n                                                                        children: \"Draft\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full bg-accent animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-2xl mb-3 group-hover:text-accent transition-colors duration-300\",\n                                                        children: draft.title || \"Untitled Event\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    draft.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mb-2 bg-primary/10 text-primary border-primary/20\",\n                                                        children: draft.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        className: \"w-48\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                asChild: true,\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/drafts/edit/\".concat(draft.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Edit Draft\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2 text-destructive focus:text-destructive\",\n                                                                onClick: ()=>handleDeleteDraft(draft.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Delete Draft\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        draft.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl bg-muted/20 border border-white/10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground line-clamp-3 leading-relaxed\",\n                                                children: draft.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatDate(draft.date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-primary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatTime(draft.time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-secondary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-secondary-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Location\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: draft.location || \"No location set\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-muted-foreground bg-muted/10 rounded-lg p-3 border border-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Created \",\n                                                                new Date(draft.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated \",\n                                                                new Date(draft.updated_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>handlePublishDraft(draft.id, draft.title),\n                                                    disabled: publishingDrafts.has(draft.id),\n                                                    size: \"lg\",\n                                                    className: \"flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                                    children: publishingDrafts.has(draft.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Publishing...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"\\uD83D\\uDE80 Launch Event\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 opacity-70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-white/20 hover:bg-accent/10 hover:border-accent/30 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/drafts/edit/\".concat(draft.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, draft.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(DraftsPage, \"Rp8Sy1C6itTUHDXLMhZBa+GMjzM=\");\n_c = DraftsPage;\nvar _c;\n$RefreshReg$(_c, \"DraftsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/drafts/page.tsx\n"));

/***/ })

});