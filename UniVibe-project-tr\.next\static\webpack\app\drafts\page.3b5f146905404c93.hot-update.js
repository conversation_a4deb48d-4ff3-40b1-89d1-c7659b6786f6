"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/drafts/page",{

/***/ "(app-pages-browser)/./app/drafts/page.tsx":
/*!*****************************!*\
  !*** ./app/drafts/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DraftsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_events__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/events */ \"(app-pages-browser)/./lib/events.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DraftsPage() {\n    _s();\n    const [drafts, setDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [filteredDrafts, setFilteredDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [publishingDrafts, setPublishingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [deletingDrafts, setDeletingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"All\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"updated\");\n    // Load drafts on component mount and when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            loadDrafts();\n            // Add event listener for when the page becomes visible (user returns from edit page)\n            const handleVisibilityChange = {\n                \"DraftsPage.useEffect.handleVisibilityChange\": ()=>{\n                    if (!document.hidden) {\n                        loadDrafts();\n                    }\n                }\n            }[\"DraftsPage.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            // Also listen for focus events\n            const handleFocus = {\n                \"DraftsPage.useEffect.handleFocus\": ()=>{\n                    loadDrafts();\n                }\n            }[\"DraftsPage.useEffect.handleFocus\"];\n            window.addEventListener('focus', handleFocus);\n            return ({\n                \"DraftsPage.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    window.removeEventListener('focus', handleFocus);\n                }\n            })[\"DraftsPage.useEffect\"];\n        }\n    }[\"DraftsPage.useEffect\"], []);\n    const loadDrafts = ()=>{\n        // First clean up any corrupted data\n        (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.cleanupDrafts)();\n        const draftData = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.getDrafts)();\n        const draftArray = Object.values(draftData);\n        // Deduplicate drafts by ID to prevent React key conflicts\n        const uniqueDrafts = draftArray.reduce((acc, draft)=>{\n            // Only keep the draft if we haven't seen this ID before\n            if (!acc.some((existingDraft)=>existingDraft.id === draft.id)) {\n                acc.push(draft);\n            }\n            return acc;\n        }, []);\n        // Debug logging (remove in production)\n        if (true) {\n            console.log('Raw draft data:', draftData);\n            console.log('Draft array:', draftArray);\n            console.log('Unique drafts:', uniqueDrafts);\n            console.log('Draft IDs:', uniqueDrafts.map((d)=>d.id));\n        }\n        setDrafts(uniqueDrafts);\n    };\n    // Filter and sort drafts\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            let filtered = [\n                ...drafts\n            ];\n            // Apply search filter\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.title.toLowerCase().includes(searchQuery.toLowerCase()) || draft.description.toLowerCase().includes(searchQuery.toLowerCase()) || draft.location.toLowerCase().includes(searchQuery.toLowerCase())\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply category filter\n            if (selectedCategory !== \"All\") {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.category === selectedCategory\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply sorting\n            filtered.sort({\n                \"DraftsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case \"title\":\n                            return a.title.localeCompare(b.title);\n                        case \"created\":\n                            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                        case \"category\":\n                            return a.category.localeCompare(b.category);\n                        default:\n                            return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n                    }\n                }\n            }[\"DraftsPage.useEffect\"]);\n            setFilteredDrafts(filtered);\n        }\n    }[\"DraftsPage.useEffect\"], [\n        drafts,\n        searchQuery,\n        selectedCategory,\n        sortBy\n    ]);\n    // Get unique categories\n    const categories = [\n        \"All\",\n        ...Array.from(new Set(drafts.map((draft)=>draft.category).filter(Boolean)))\n    ];\n    const handlePublishDraft = async (draftId, draftTitle)=>{\n        setPublishingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const { data, error } = await (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.publishDraft)(draftId);\n            if (error) {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to publish draft\", error);\n                return;\n            }\n            // Success!\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showEventCreatedNotification)(draftTitle, false, data === null || data === void 0 ? void 0 : data.id);\n            loadDrafts() // Refresh the drafts list\n            ;\n        } catch (error) {\n            console.error(\"Error publishing draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setPublishingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const handleDeleteDraft = async (draftId)=>{\n        setDeletingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const success = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.deleteDraft)(draftId);\n            if (success) {\n                loadDrafts() // Refresh the drafts list\n                ;\n            } else {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to delete draft\", \"Please try again\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setDeletingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"No date set\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const formatTime = (timeString)=>{\n        if (!timeString) return \"No time set\";\n        const [hours, minutes] = timeString.split(':');\n        const date = new Date();\n        date.setHours(parseInt(hours), parseInt(minutes));\n        return date.toLocaleTimeString('en-US', {\n            hour: 'numeric',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-background via-background to-primary/5\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-2xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 text-accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-bold text-white\",\n                                                        children: drafts.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold bg-gradient-to-r from-white via-white to-accent bg-clip-text text-transparent mb-2\",\n                                                    children: \"Draft Studio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground text-lg\",\n                                                    children: \"Craft, refine, and launch your events when the moment is perfect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    asChild: true,\n                                    size: \"lg\",\n                                    className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/post\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:rotate-90 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create New Event\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 opacity-70\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex flex-col sm:flex-row gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-white/10 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                            placeholder: \"Search drafts by title, description, or location...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pl-10 bg-background/50 border-white/20 focus:border-accent/50 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedCategory\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSelectedCategory(category),\n                                                    className: selectedCategory === category ? \"bg-accent/10\" : \"\",\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sort\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"updated\"),\n                                                    className: sortBy === \"updated\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Last Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"created\"),\n                                                    className: sortBy === \"created\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Date Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"title\"),\n                                                    className: sortBy === \"title\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Title A-Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"category\"),\n                                                    className: sortBy === \"category\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        drafts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        filteredDrafts.length,\n                                        \" of \",\n                                        drafts.length,\n                                        \" drafts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, this),\n                                        'Filtered by \"',\n                                        searchQuery,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCategory !== \"All\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Category: \",\n                                        selectedCategory\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this),\n                drafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-2xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 rounded-3xl bg-gradient-to-br from-accent/20 to-primary/20 w-fit mx-auto border border-accent/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 text-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent\",\n                                children: \"Your Draft Studio Awaits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-lg mb-8 max-w-md mx-auto\",\n                                children: \"Transform your event ideas into reality. Start crafting your first event and save it as a draft to perfect every detail.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        asChild: true,\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/post\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Your First Event\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 opacity-70\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        className: \"border-white/20 hover:bg-accent/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Browse Examples\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 11\n                }, this) : filteredDrafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 rounded-2xl bg-muted/20 w-fit mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"No drafts match your search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: \"Try adjusting your search terms or filters to find what you're looking for\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setSelectedCategory(\"All\");\n                                },\n                                variant: \"outline\",\n                                className: \"border-white/20 hover:bg-accent/10\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8\",\n                    children: filteredDrafts.map((draft, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"group shadow-xl border-white/10 hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm hover:scale-[1.02] hover:border-accent/30\",\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-accent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs bg-accent/10 border-accent/30 text-accent\",\n                                                                        children: \"Draft\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full bg-accent animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-2xl mb-3 group-hover:text-accent transition-colors duration-300\",\n                                                        children: draft.title || \"Untitled Event\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    draft.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mb-2 bg-primary/10 text-primary border-primary/20\",\n                                                        children: draft.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        className: \"w-48\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                asChild: true,\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/drafts/edit/\".concat(draft.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Edit Draft\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2 text-destructive focus:text-destructive\",\n                                                                onClick: ()=>handleDeleteDraft(draft.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Delete Draft\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        draft.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl bg-muted/20 border border-white/10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground line-clamp-3 leading-relaxed\",\n                                                children: draft.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatDate(draft.date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-primary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatTime(draft.time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-secondary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-secondary-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Location\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: draft.location || \"No location set\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-muted-foreground bg-muted/10 rounded-lg p-3 border border-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Created \",\n                                                                new Date(draft.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated \",\n                                                                new Date(draft.updated_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>handlePublishDraft(draft.id, draft.title),\n                                                    disabled: publishingDrafts.has(draft.id),\n                                                    size: \"lg\",\n                                                    className: \"flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                                    children: publishingDrafts.has(draft.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Publishing...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"\\uD83D\\uDE80 Launch Event\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 opacity-70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-white/20 hover:bg-accent/10 hover:border-accent/30 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/drafts/edit/\".concat(draft.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, draft.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n_s(DraftsPage, \"Rp8Sy1C6itTUHDXLMhZBa+GMjzM=\");\n_c = DraftsPage;\nvar _c;\n$RefreshReg$(_c, \"DraftsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/drafts/page.tsx\n"));

/***/ })

});