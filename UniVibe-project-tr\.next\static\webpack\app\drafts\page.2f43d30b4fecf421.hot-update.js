"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/drafts/page",{

/***/ "(app-pages-browser)/./app/drafts/page.tsx":
/*!*****************************!*\
  !*** ./app/drafts/page.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DraftsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Clock,Edit3,Eye,FileText,Filter,MapPin,MoreVertical,Plus,Search,Sparkles,Trash2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_events__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/events */ \"(app-pages-browser)/./lib/events.ts\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/notification */ \"(app-pages-browser)/./components/ui/notification.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DraftsPage() {\n    _s();\n    const [drafts, setDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [filteredDrafts, setFilteredDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [publishingDrafts, setPublishingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [deletingDrafts, setDeletingDrafts] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Set());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"All\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"updated\");\n    // Load drafts on component mount and when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            loadDrafts();\n            // Add event listener for when the page becomes visible (user returns from edit page)\n            const handleVisibilityChange = {\n                \"DraftsPage.useEffect.handleVisibilityChange\": ()=>{\n                    if (!document.hidden) {\n                        loadDrafts();\n                    }\n                }\n            }[\"DraftsPage.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            // Also listen for focus events\n            const handleFocus = {\n                \"DraftsPage.useEffect.handleFocus\": ()=>{\n                    loadDrafts();\n                }\n            }[\"DraftsPage.useEffect.handleFocus\"];\n            window.addEventListener('focus', handleFocus);\n            return ({\n                \"DraftsPage.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    window.removeEventListener('focus', handleFocus);\n                }\n            })[\"DraftsPage.useEffect\"];\n        }\n    }[\"DraftsPage.useEffect\"], []);\n    const loadDrafts = ()=>{\n        const draftData = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.getDrafts)();\n        const draftArray = Object.values(draftData);\n        // Deduplicate drafts by ID to prevent React key conflicts\n        const uniqueDrafts = draftArray.reduce((acc, draft)=>{\n            // Only keep the draft if we haven't seen this ID before\n            if (!acc.some((existingDraft)=>existingDraft.id === draft.id)) {\n                acc.push(draft);\n            }\n            return acc;\n        }, []);\n        setDrafts(uniqueDrafts);\n    };\n    // Filter and sort drafts\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DraftsPage.useEffect\": ()=>{\n            let filtered = [\n                ...drafts\n            ];\n            // Apply search filter\n            if (searchQuery) {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.title.toLowerCase().includes(searchQuery.toLowerCase()) || draft.description.toLowerCase().includes(searchQuery.toLowerCase()) || draft.location.toLowerCase().includes(searchQuery.toLowerCase())\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply category filter\n            if (selectedCategory !== \"All\") {\n                filtered = filtered.filter({\n                    \"DraftsPage.useEffect\": (draft)=>draft.category === selectedCategory\n                }[\"DraftsPage.useEffect\"]);\n            }\n            // Apply sorting\n            filtered.sort({\n                \"DraftsPage.useEffect\": (a, b)=>{\n                    switch(sortBy){\n                        case \"title\":\n                            return a.title.localeCompare(b.title);\n                        case \"created\":\n                            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n                        case \"category\":\n                            return a.category.localeCompare(b.category);\n                        default:\n                            return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();\n                    }\n                }\n            }[\"DraftsPage.useEffect\"]);\n            setFilteredDrafts(filtered);\n        }\n    }[\"DraftsPage.useEffect\"], [\n        drafts,\n        searchQuery,\n        selectedCategory,\n        sortBy\n    ]);\n    // Get unique categories\n    const categories = [\n        \"All\",\n        ...Array.from(new Set(drafts.map((draft)=>draft.category).filter(Boolean)))\n    ];\n    const handlePublishDraft = async (draftId, draftTitle)=>{\n        setPublishingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const { data, error } = await (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.publishDraft)(draftId);\n            if (error) {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to publish draft\", error);\n                return;\n            }\n            // Success!\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showEventCreatedNotification)(draftTitle, false, data === null || data === void 0 ? void 0 : data.id);\n            loadDrafts() // Refresh the drafts list\n            ;\n        } catch (error) {\n            console.error(\"Error publishing draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setPublishingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const handleDeleteDraft = async (draftId)=>{\n        setDeletingDrafts((prev)=>new Set(prev).add(draftId));\n        try {\n            const success = (0,_lib_events__WEBPACK_IMPORTED_MODULE_5__.deleteDraft)(draftId);\n            if (success) {\n                loadDrafts() // Refresh the drafts list\n                ;\n            } else {\n                (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Failed to delete draft\", \"Please try again\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting draft:\", error);\n            (0,_components_ui_notification__WEBPACK_IMPORTED_MODULE_6__.showError)(\"Something went wrong\", \"Please try again later\");\n        } finally{\n            setDeletingDrafts((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(draftId);\n                return newSet;\n            });\n        }\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"No date set\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    const formatTime = (timeString)=>{\n        if (!timeString) return \"No time set\";\n        const [hours, minutes] = timeString.split(':');\n        const date = new Date();\n        date.setHours(parseInt(hours), parseInt(minutes));\n        return date.toLocaleTimeString('en-US', {\n            hour: 'numeric',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-background via-background to-primary/5\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 rounded-2xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-8 w-8 text-accent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-bold text-white\",\n                                                        children: drafts.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-4xl font-bold bg-gradient-to-r from-white via-white to-accent bg-clip-text text-transparent mb-2\",\n                                                    children: \"Draft Studio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground text-lg\",\n                                                    children: \"Craft, refine, and launch your events when the moment is perfect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    asChild: true,\n                                    size: \"lg\",\n                                    className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/post\",\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 group-hover:rotate-90 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Create New Event\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 opacity-70\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 flex flex-col sm:flex-row gap-4 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-white/10 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                            placeholder: \"Search drafts by title, description, or location...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pl-10 bg-background/50 border-white/20 focus:border-accent/50 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    selectedCategory\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSelectedCategory(category),\n                                                    className: selectedCategory === category ? \"bg-accent/10\" : \"\",\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"bg-background/50 border-white/20 hover:bg-accent/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sort\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            className: \"w-48\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"updated\"),\n                                                    className: sortBy === \"updated\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Last Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"created\"),\n                                                    className: sortBy === \"created\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Date Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"title\"),\n                                                    className: sortBy === \"title\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Title A-Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                    onClick: ()=>setSortBy(\"category\"),\n                                                    className: sortBy === \"category\" ? \"bg-accent/10\" : \"\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        drafts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex flex-wrap gap-4 text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        filteredDrafts.length,\n                                        \" of \",\n                                        drafts.length,\n                                        \" drafts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        'Filtered by \"',\n                                        searchQuery,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCategory !== \"All\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Category: \",\n                                        selectedCategory\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this),\n                drafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-2xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-16 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 rounded-3xl bg-gradient-to-br from-accent/20 to-primary/20 w-fit mx-auto border border-accent/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 text-accent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent\",\n                                children: \"Your Draft Studio Awaits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-lg mb-8 max-w-md mx-auto\",\n                                children: \"Transform your event ideas into reality. Start crafting your first event and save it as a draft to perfect every detail.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        asChild: true,\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/post\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 group-hover:scale-110 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Your First Event\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 opacity-70\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        className: \"border-white/20 hover:bg-accent/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Browse Examples\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, this) : filteredDrafts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"shadow-xl border-white/10 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 rounded-2xl bg-muted/20 w-fit mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: \"No drafts match your search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: \"Try adjusting your search terms or filters to find what you're looking for\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setSelectedCategory(\"All\");\n                                },\n                                variant: \"outline\",\n                                className: \"border-white/20 hover:bg-accent/10\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8\",\n                    children: filteredDrafts.map((draft, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"group shadow-xl border-white/10 hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm hover:scale-[1.02] hover:border-accent/30\",\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"pb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-accent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs bg-accent/10 border-accent/30 text-accent\",\n                                                                        children: \"Draft\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full bg-accent animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-2xl mb-3 group-hover:text-accent transition-colors duration-300\",\n                                                        children: draft.title || \"Untitled Event\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    draft.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mb-2 bg-primary/10 text-primary border-primary/20\",\n                                                        children: draft.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        className: \"w-48\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                asChild: true,\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"/drafts/edit/\".concat(draft.id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Edit Draft\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                className: \"flex items-center gap-2 text-destructive focus:text-destructive\",\n                                                                onClick: ()=>handleDeleteDraft(draft.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Delete Draft\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        draft.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl bg-muted/20 border border-white/10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground line-clamp-3 leading-relaxed\",\n                                                children: draft.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-accent/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-accent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatDate(draft.date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-primary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatTime(draft.time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 p-3 rounded-lg bg-background/50 border border-white/10 hover:border-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-secondary/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-secondary-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-muted-foreground uppercase tracking-wide\",\n                                                                    children: \"Location\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: draft.location || \"No location set\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-muted-foreground bg-muted/10 rounded-lg p-3 border border-white/10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Created \",\n                                                                new Date(draft.created_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Updated \",\n                                                                new Date(draft.updated_at).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3 pt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>handlePublishDraft(draft.id, draft.title),\n                                                    disabled: publishingDrafts.has(draft.id),\n                                                    size: \"lg\",\n                                                    className: \"flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                                                    children: publishingDrafts.has(draft.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Publishing...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"\\uD83D\\uDE80 Launch Event\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2 opacity-70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    asChild: true,\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-white/20 hover:bg-accent/10 hover:border-accent/30 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/drafts/edit/\".concat(draft.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Clock_Edit3_Eye_FileText_Filter_MapPin_MoreVertical_Plus_Search_Sparkles_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, draft.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\UniVibe\\\\UniVibe-project-tr\\\\app\\\\drafts\\\\page.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(DraftsPage, \"Rp8Sy1C6itTUHDXLMhZBa+GMjzM=\");\n_c = DraftsPage;\nvar _c;\n$RefreshReg$(_c, \"DraftsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/drafts/page.tsx\n"));

/***/ })

});