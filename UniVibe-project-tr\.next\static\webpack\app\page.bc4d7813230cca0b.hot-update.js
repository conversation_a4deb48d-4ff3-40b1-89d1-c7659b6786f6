"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/events.ts":
/*!***********************!*\
  !*** ./lib/events.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupDrafts: () => (/* binding */ cleanupDrafts),\n/* harmony export */   createEvent: () => (/* binding */ createEvent),\n/* harmony export */   deleteDraft: () => (/* binding */ deleteDraft),\n/* harmony export */   deleteEvent: () => (/* binding */ deleteEvent),\n/* harmony export */   getDrafts: () => (/* binding */ getDrafts),\n/* harmony export */   getEventById: () => (/* binding */ getEventById),\n/* harmony export */   getEvents: () => (/* binding */ getEvents),\n/* harmony export */   publishDraft: () => (/* binding */ publishDraft),\n/* harmony export */   saveDraft: () => (/* binding */ saveDraft),\n/* harmony export */   updateEvent: () => (/* binding */ updateEvent),\n/* harmony export */   uploadEventImage: () => (/* binding */ uploadEventImage),\n/* harmony export */   validateEventForm: () => (/* binding */ validateEventForm)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Create a new event\nasync function createEvent(eventData) {\n    try {\n        console.log('Creating event with data:', eventData);\n        const insertData = {\n            title: eventData.title,\n            description: eventData.description,\n            date: eventData.date,\n            time: eventData.time,\n            location: eventData.location,\n            category: eventData.category,\n            organizer: eventData.organizer,\n            organizer_id: eventData.organizer_id,\n            image_url: eventData.image_url,\n            attendees_count: 0\n        };\n        console.log('Insert data:', insertData);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').insert(insertData).select().single();\n        if (error) {\n            console.error('Error creating event:', error);\n            return {\n                data: null,\n                error: error.message || 'Unknown database error'\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error creating event:', error);\n        return {\n            data: null,\n            error: 'An unexpected error occurred'\n        };\n    }\n}\n// Save event as draft (using a separate drafts table or a flag)\nasync function saveDraft(eventData) {\n    try {\n        // For now, we'll save drafts in localStorage\n        // In a real app, you might want a separate drafts table or a is_draft flag\n        const drafts = getDrafts();\n        // Use existing ID if provided (for updates), otherwise create new one\n        const draftId = eventData.id || \"draft_\".concat(Date.now());\n        const existingDraft = drafts[draftId];\n        const draftToSave = {\n            id: draftId,\n            ...eventData,\n            created_at: eventData.created_at || (existingDraft === null || existingDraft === void 0 ? void 0 : existingDraft.created_at) || new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        drafts[draftId] = draftToSave;\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return {\n            data: draftToSave,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error saving draft:', error);\n        return {\n            data: null,\n            error: 'Failed to save draft'\n        };\n    }\n}\n// Get all drafts\nfunction getDrafts() {\n    try {\n        const drafts = localStorage.getItem('event_drafts');\n        const parsedDrafts = drafts ? JSON.parse(drafts) : {};\n        // Clean up any potential duplicates or invalid entries\n        const cleanedDrafts = {};\n        Object.entries(parsedDrafts).forEach((param)=>{\n            let [key, draft] = param;\n            if (draft && draft.id && key === draft.id) {\n                cleanedDrafts[key] = draft;\n            }\n        });\n        // Save cleaned drafts back if there were any changes\n        if (Object.keys(cleanedDrafts).length !== Object.keys(parsedDrafts).length) {\n            localStorage.setItem('event_drafts', JSON.stringify(cleanedDrafts));\n        }\n        return cleanedDrafts;\n    } catch (error) {\n        console.error('Error getting drafts:', error);\n        return {};\n    }\n}\n// Delete a draft\nfunction deleteDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        delete drafts[draftId];\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return true;\n    } catch (error) {\n        console.error('Error deleting draft:', error);\n        return false;\n    }\n}\n// Clean up drafts storage (utility function for debugging)\nfunction cleanupDrafts() {\n    try {\n        const drafts = getDrafts();\n        const cleanedDrafts = {};\n        // Only keep valid drafts with proper structure\n        Object.entries(drafts).forEach((param)=>{\n            let [key, draft] = param;\n            if (draft && draft.id && key === draft.id && typeof draft.title === 'string' && typeof draft.created_at === 'string') {\n                cleanedDrafts[key] = draft;\n            }\n        });\n        localStorage.setItem('event_drafts', JSON.stringify(cleanedDrafts));\n        console.log('Drafts cleaned up. Remaining drafts:', Object.keys(cleanedDrafts).length);\n    } catch (error) {\n        console.error('Error cleaning up drafts:', error);\n    }\n}\n// Publish a draft as a live event\nasync function publishDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        const draft = drafts[draftId];\n        if (!draft) {\n            return {\n                data: null,\n                error: 'Draft not found'\n            };\n        }\n        // Validate required fields\n        if (!draft.title || !draft.date || !draft.time || !draft.location || !draft.category || !draft.organizer) {\n            return {\n                data: null,\n                error: 'Draft is missing required fields. Please edit the draft and ensure all fields are filled.'\n            };\n        }\n        // Create the event from the draft\n        const { data, error } = await createEvent({\n            title: draft.title,\n            description: draft.description || '',\n            date: draft.date,\n            time: draft.time,\n            location: draft.location,\n            category: draft.category,\n            organizer: draft.organizer,\n            organizer_id: draft.organizer_id || null,\n            image_url: draft.image_url || null\n        });\n        if (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n        // Delete the draft after successful publication\n        deleteDraft(draftId);\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error publishing draft:', error);\n        return {\n            data: null,\n            error: 'Failed to publish draft'\n        };\n    }\n}\n// Upload image to Supabase Storage\nasync function uploadEventImage(file, eventId) {\n    try {\n        console.log('Uploading image:', {\n            fileName: file.name,\n            fileSize: file.size,\n            eventId\n        });\n        // Validate file\n        if (!file) {\n            return {\n                url: null,\n                error: 'No file provided'\n            };\n        }\n        // Check file size (50MB limit)\n        if (file.size > 50 * 1024 * 1024) {\n            return {\n                url: null,\n                error: 'File size too large. Maximum size is 50MB.'\n            };\n        }\n        // Check file type\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/webp',\n            'image/gif'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                url: null,\n                error: 'Invalid file type. Please upload a JPEG, PNG, WebP, or GIF image.'\n            };\n        }\n        const fileExt = file.name.split('.').pop();\n        const fileName = \"\".concat(eventId, \"_\").concat(Date.now(), \".\").concat(fileExt);\n        const filePath = \"event-images/\".concat(fileName);\n        console.log('Uploading to path:', filePath);\n        const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').upload(filePath, file, {\n            cacheControl: '3600',\n            upsert: true\n        });\n        if (uploadError) {\n            console.error('Error uploading image:', uploadError);\n            return {\n                url: null,\n                error: uploadError.message || 'Failed to upload image to storage'\n            };\n        }\n        console.log('Upload successful:', uploadData);\n        const { data } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').getPublicUrl(filePath);\n        console.log('Public URL generated:', data.publicUrl);\n        return {\n            url: data.publicUrl,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error uploading image:', error);\n        return {\n            url: null,\n            error: 'Failed to upload image'\n        };\n    }\n}\n// Get all events\nasync function getEvents() {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').order('date', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error fetching events:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching events:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch events'\n        };\n    }\n}\n// Get event by ID\nasync function getEventById(id) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').eq('id', id).single();\n        if (error) {\n            console.error('Error fetching event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching event:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch event'\n        };\n    }\n}\n// Update event\nasync function updateEvent(id, updates) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').update(updates).eq('id', id).select().single();\n        if (error) {\n            console.error('Error updating event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error updating event:', error);\n        return {\n            data: null,\n            error: 'Failed to update event'\n        };\n    }\n}\n// Delete event\nasync function deleteEvent(id) {\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').delete().eq('id', id);\n        if (error) {\n            console.error('Error deleting event:', error);\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error deleting event:', error);\n        return {\n            error: 'Failed to delete event'\n        };\n    }\n}\n// Validate event form data\nfunction validateEventForm(formData) {\n    const errors = [];\n    if (!formData.title.trim()) {\n        errors.push('Event title is required');\n    }\n    if (!formData.description.trim()) {\n        errors.push('Event description is required');\n    }\n    if (!formData.date) {\n        errors.push('Event date is required');\n    } else {\n        const eventDate = new Date(formData.date);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        if (eventDate < today) {\n            errors.push('Event date cannot be in the past');\n        }\n    }\n    if (!formData.time) {\n        errors.push('Event time is required');\n    }\n    if (!formData.venue.trim()) {\n        errors.push('Event venue is required');\n    }\n    if (!formData.category) {\n        errors.push('Event category is required');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/events.ts\n"));

/***/ })

});