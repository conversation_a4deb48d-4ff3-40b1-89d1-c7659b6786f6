"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Heart, Share2, MapPin, Calendar, Users, User, FileText, Sparkles } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState, useEffect, use } from "react"
import { cn } from "@/lib/utils"
import { getEventById } from "@/lib/events"

// Mock event data
const mockEvent = {
  id: "1",
  title: "Spring Music Festival 2024",
  date: "March 15, 2024",
  time: "7:00 PM - 11:00 PM",
  location: "University Quad, Main Campus",
  organizer: "Music Society",
  organizerAvatar: "/placeholder.svg?height=40&width=40",
  attendees: 234,
  category: "Music",
  image: "/placeholder.svg?height=300&width=600",
  description: `Join us for the biggest music event of the spring semester! The Spring Music Festival features performances from local bands, student artists, and special guest performers.

🎵 Featured Acts:
- The Campus Collective
- Midnight Echoes  
- DJ Sarah K
- Open Mic Session

🍕 Food trucks will be available
🎟️ Free admission for all students
🎁 Prizes and giveaways throughout the night

Bring your friends and enjoy an unforgettable evening of music, food, and community. Don't forget to bring your student ID for entry!`,
  isRSVPed: false,
}

export default function EventDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const [event, setEvent] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [rsvped, setRsvped] = useState(false)
  const [attendeeCount, setAttendeeCount] = useState(0)

  // Fetch event data
  useEffect(() => {
    const fetchEvent = async () => {
      try {
        const { data, error } = await getEventById(resolvedParams.id)
        if (error) {
          console.error('Error fetching event:', error)
          // Fallback to mock data
          setEvent(mockEvent)
          setRsvped(mockEvent.isRSVPed)
          setAttendeeCount(mockEvent.attendees)
        } else if (data) {
          setEvent(data)
          setRsvped(false) // TODO: Check if user has RSVPed
          setAttendeeCount(data.attendees_count || 0)
        } else {
          // Event not found, use mock data
          setEvent(mockEvent)
          setRsvped(mockEvent.isRSVPed)
          setAttendeeCount(mockEvent.attendees)
        }
      } catch (error) {
        console.error('Unexpected error fetching event:', error)
        setEvent(mockEvent)
        setRsvped(mockEvent.isRSVPed)
        setAttendeeCount(mockEvent.attendees)
      } finally {
        setLoading(false)
      }
    }

    fetchEvent()
  }, [resolvedParams.id])

  const handleRSVP = () => {
    setRsvped(!rsvped)
    setAttendeeCount((prev) => (rsvped ? prev - 1 : prev + 1))
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: event?.title || 'Event',
          text: `Check out this event: ${event?.title || 'Event'}`,
          url: window.location.href,
        })
      } catch (error) {
        console.log("Error sharing:", error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Event link copied to clipboard!")
    }
  }

  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "TBD"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Helper function to format time
  const formatTime = (timeString: string) => {
    if (!timeString) return "TBD"
    const [hours, minutes] = timeString.split(':')
    const date = new Date()
    date.setHours(parseInt(hours), parseInt(minutes))
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          <div className="h-64 bg-muted rounded-lg"></div>
          <div className="h-8 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="p-4 text-center">
        <h1 className="text-2xl font-bold mb-2">Event Not Found</h1>
        <p className="text-muted-foreground mb-4">The event you're looking for doesn't exist.</p>
        <Link href="/">
          <Button>Back to Events</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Back button */}
      <Link href="/">
        <Button variant="ghost" size="icon" className="absolute top-4 left-4 z-10 bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-all">
          <ArrowLeft className="h-5 w-5" />
        </Button>
      </Link>

      {/* Desktop Layout */}
      <div className="hidden lg:block">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Left Column - Event Poster */}
            <div className="space-y-6">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl border border-white/10">
                <div className="aspect-[4/5] relative">
                  <Image
                    src={event.image_url || "/placeholder.svg"}
                    alt={event.title}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
                  <Badge className="absolute top-4 left-4 bg-primary/90 text-white border-0 backdrop-blur-sm">
                    <Sparkles className="w-3 h-3 mr-1" />
                    {event.category}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Right Column - Event Details */}
            <div className="space-y-8">
              {/* Title and Actions */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    {event.title}
                  </h1>
                  <div className="flex items-center gap-3 text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>Organized by {event.organizer}</span>
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button
                    onClick={handleRSVP}
                    size="lg"
                    className={cn(
                      "flex-1 flex items-center gap-2 h-12",
                      rsvped ? "bg-accent hover:bg-accent/90 text-accent-foreground" : "bg-primary hover:bg-primary/90",
                    )}
                  >
                    <Heart className={cn("h-5 w-5", rsvped && "fill-current")} />
                    {rsvped ? "Going!" : "I'm Interested"}
                  </Button>

                  <Button variant="outline" size="lg" onClick={handleShare} className="border-white/20 hover:bg-accent/10">
                    <Share2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              {/* Event Details Card */}
              <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
                <CardContent className="p-6 space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/30">
                      <Calendar className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="font-semibold text-lg">{formatDate(event.date)}</p>
                      <p className="text-muted-foreground">{formatTime(event.time)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-accent/20 to-primary/20 border border-accent/30">
                      <MapPin className="h-6 w-6 text-accent" />
                    </div>
                    <div>
                      <p className="font-semibold">{event.location}</p>
                      <p className="text-sm text-muted-foreground">Event Location</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/30">
                      <Users className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <p className="font-semibold">{attendeeCount} people going</p>
                      <p className="text-sm text-muted-foreground">Join the community</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Description */}
              <Card className="shadow-xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-xl mb-4 flex items-center gap-2">
                    <FileText className="h-5 w-5 text-accent" />
                    About this event
                  </h3>
                  <div className="prose prose-sm max-w-none text-muted-foreground">
                    {(event.description || 'No description available.').split("\n").map((paragraph, index) => (
                      <p key={index} className="mb-3 last:mb-0 leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* Header Image */}
        <div className="relative h-64 bg-muted">
          <Image src={event.image_url || "/placeholder.svg"} alt={event.title} fill className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <Badge className="absolute bottom-4 left-4 bg-primary text-primary-foreground">{event.category}</Badge>
        </div>

        {/* Content */}
        <div className="p-4 space-y-6">
        {/* Title and Actions */}
        <div className="space-y-4">
          <h1 className="text-2xl font-bold">{event.title}</h1>

          <div className="flex gap-2">
            <Button
              onClick={handleRSVP}
              className={cn(
                "flex-1 flex items-center gap-2",
                rsvped ? "bg-accent hover:bg-accent/90 text-accent-foreground" : "bg-primary hover:bg-primary/90",
              )}
            >
              <Heart className={cn("h-4 w-4", rsvped && "fill-current")} />
              {rsvped ? "Going!" : "I'm Interested"}
            </Button>

            <Button variant="outline" size="icon" onClick={handleShare}>
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Event Details */}
        <Card>
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-primary" />
              <div>
                <p className="font-medium">{formatDate(event.date)}</p>
                <p className="text-sm text-muted-foreground">{formatTime(event.time)}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-primary" />
              <p>{event.location}</p>
            </div>

            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-primary" />
              <p>{attendeeCount} people going</p>
            </div>
          </CardContent>
        </Card>

        {/* Organizer */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-primary-foreground" />
              </div>
              <div>
                <p className="font-medium">Organized by</p>
                <p className="text-sm text-muted-foreground">{event.organizer}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-3">About this event</h3>
            <div className="prose prose-sm max-w-none">
              {(event.description || 'No description available.').split("\n").map((paragraph, index) => (
                <p key={index} className="mb-3 last:mb-0 text-muted-foreground">
                  {paragraph}
                </p>
              ))}
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
  )
}
