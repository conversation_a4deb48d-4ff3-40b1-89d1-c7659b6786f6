"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/event/[id]/page",{

/***/ "(app-pages-browser)/./lib/events.ts":
/*!***********************!*\
  !*** ./lib/events.ts ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEvent: () => (/* binding */ createEvent),\n/* harmony export */   deleteDraft: () => (/* binding */ deleteDraft),\n/* harmony export */   deleteEvent: () => (/* binding */ deleteEvent),\n/* harmony export */   getDrafts: () => (/* binding */ getDrafts),\n/* harmony export */   getEventById: () => (/* binding */ getEventById),\n/* harmony export */   getEvents: () => (/* binding */ getEvents),\n/* harmony export */   publishDraft: () => (/* binding */ publishDraft),\n/* harmony export */   saveDraft: () => (/* binding */ saveDraft),\n/* harmony export */   updateEvent: () => (/* binding */ updateEvent),\n/* harmony export */   uploadEventImage: () => (/* binding */ uploadEventImage),\n/* harmony export */   validateEventForm: () => (/* binding */ validateEventForm)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n\n// Create a new event\nasync function createEvent(eventData) {\n    try {\n        console.log('Creating event with data:', eventData);\n        const insertData = {\n            title: eventData.title,\n            description: eventData.description,\n            date: eventData.date,\n            time: eventData.time,\n            location: eventData.location,\n            category: eventData.category,\n            organizer: eventData.organizer,\n            organizer_id: eventData.organizer_id,\n            image_url: eventData.image_url,\n            attendees_count: 0\n        };\n        console.log('Insert data:', insertData);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').insert(insertData).select().single();\n        if (error) {\n            console.error('Error creating event:', error);\n            return {\n                data: null,\n                error: error.message || 'Unknown database error'\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error creating event:', error);\n        return {\n            data: null,\n            error: 'An unexpected error occurred'\n        };\n    }\n}\n// Save event as draft (using a separate drafts table or a flag)\nasync function saveDraft(eventData) {\n    try {\n        // For now, we'll save drafts in localStorage\n        // In a real app, you might want a separate drafts table or a is_draft flag\n        const drafts = getDrafts();\n        // Use existing ID if provided (for updates), otherwise create new one\n        const draftId = eventData.id || \"draft_\".concat(Date.now());\n        const existingDraft = drafts[draftId];\n        const draftToSave = {\n            id: draftId,\n            ...eventData,\n            created_at: eventData.created_at || (existingDraft === null || existingDraft === void 0 ? void 0 : existingDraft.created_at) || new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        drafts[draftId] = draftToSave;\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return {\n            data: draftToSave,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error saving draft:', error);\n        return {\n            data: null,\n            error: 'Failed to save draft'\n        };\n    }\n}\n// Get all drafts\nfunction getDrafts() {\n    try {\n        const drafts = localStorage.getItem('event_drafts');\n        const parsedDrafts = drafts ? JSON.parse(drafts) : {};\n        // Clean up any potential duplicates or invalid entries\n        const cleanedDrafts = {};\n        Object.entries(parsedDrafts).forEach((param)=>{\n            let [key, draft] = param;\n            if (draft && draft.id && key === draft.id) {\n                cleanedDrafts[key] = draft;\n            }\n        });\n        // Save cleaned drafts back if there were any changes\n        if (Object.keys(cleanedDrafts).length !== Object.keys(parsedDrafts).length) {\n            localStorage.setItem('event_drafts', JSON.stringify(cleanedDrafts));\n        }\n        return cleanedDrafts;\n    } catch (error) {\n        console.error('Error getting drafts:', error);\n        return {};\n    }\n}\n// Delete a draft\nfunction deleteDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        delete drafts[draftId];\n        localStorage.setItem('event_drafts', JSON.stringify(drafts));\n        return true;\n    } catch (error) {\n        console.error('Error deleting draft:', error);\n        return false;\n    }\n}\n// Publish a draft as a live event\nasync function publishDraft(draftId) {\n    try {\n        const drafts = getDrafts();\n        const draft = drafts[draftId];\n        if (!draft) {\n            return {\n                data: null,\n                error: 'Draft not found'\n            };\n        }\n        // Validate required fields\n        if (!draft.title || !draft.date || !draft.time || !draft.location || !draft.category || !draft.organizer) {\n            return {\n                data: null,\n                error: 'Draft is missing required fields. Please edit the draft and ensure all fields are filled.'\n            };\n        }\n        // Create the event from the draft\n        const { data, error } = await createEvent({\n            title: draft.title,\n            description: draft.description || '',\n            date: draft.date,\n            time: draft.time,\n            location: draft.location,\n            category: draft.category,\n            organizer: draft.organizer,\n            organizer_id: draft.organizer_id || null,\n            image_url: draft.image_url || null\n        });\n        if (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n        // Delete the draft after successful publication\n        deleteDraft(draftId);\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Error publishing draft:', error);\n        return {\n            data: null,\n            error: 'Failed to publish draft'\n        };\n    }\n}\n// Upload image to Supabase Storage\nasync function uploadEventImage(file, eventId) {\n    try {\n        console.log('Uploading image:', {\n            fileName: file.name,\n            fileSize: file.size,\n            eventId\n        });\n        // Validate file\n        if (!file) {\n            return {\n                url: null,\n                error: 'No file provided'\n            };\n        }\n        // Check file size (50MB limit)\n        if (file.size > 50 * 1024 * 1024) {\n            return {\n                url: null,\n                error: 'File size too large. Maximum size is 50MB.'\n            };\n        }\n        // Check file type\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/webp',\n            'image/gif'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return {\n                url: null,\n                error: 'Invalid file type. Please upload a JPEG, PNG, WebP, or GIF image.'\n            };\n        }\n        const fileExt = file.name.split('.').pop();\n        const fileName = \"\".concat(eventId, \"_\").concat(Date.now(), \".\").concat(fileExt);\n        const filePath = \"event-images/\".concat(fileName);\n        console.log('Uploading to path:', filePath);\n        const { data: uploadData, error: uploadError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').upload(filePath, file, {\n            cacheControl: '3600',\n            upsert: true\n        });\n        if (uploadError) {\n            console.error('Error uploading image:', uploadError);\n            return {\n                url: null,\n                error: uploadError.message || 'Failed to upload image to storage'\n            };\n        }\n        console.log('Upload successful:', uploadData);\n        const { data } = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from('events').getPublicUrl(filePath);\n        console.log('Public URL generated:', data.publicUrl);\n        return {\n            url: data.publicUrl,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error uploading image:', error);\n        return {\n            url: null,\n            error: 'Failed to upload image'\n        };\n    }\n}\n// Get all events\nasync function getEvents() {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').order('date', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error fetching events:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching events:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch events'\n        };\n    }\n}\n// Get event by ID\nasync function getEventById(id) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').select('*').eq('id', id).single();\n        if (error) {\n            console.error('Error fetching event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error fetching event:', error);\n        return {\n            data: null,\n            error: 'Failed to fetch event'\n        };\n    }\n}\n// Update event\nasync function updateEvent(id, updates) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').update(updates).eq('id', id).select().single();\n        if (error) {\n            console.error('Error updating event:', error);\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error updating event:', error);\n        return {\n            data: null,\n            error: 'Failed to update event'\n        };\n    }\n}\n// Delete event\nasync function deleteEvent(id) {\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('events').delete().eq('id', id);\n        if (error) {\n            console.error('Error deleting event:', error);\n            return {\n                error: error.message\n            };\n        }\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('Unexpected error deleting event:', error);\n        return {\n            error: 'Failed to delete event'\n        };\n    }\n}\n// Validate event form data\nfunction validateEventForm(formData) {\n    const errors = [];\n    if (!formData.title.trim()) {\n        errors.push('Event title is required');\n    }\n    if (!formData.description.trim()) {\n        errors.push('Event description is required');\n    }\n    if (!formData.date) {\n        errors.push('Event date is required');\n    } else {\n        const eventDate = new Date(formData.date);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        if (eventDate < today) {\n            errors.push('Event date cannot be in the past');\n        }\n    }\n    if (!formData.time) {\n        errors.push('Event time is required');\n    }\n    if (!formData.venue.trim()) {\n        errors.push('Event venue is required');\n    }\n    if (!formData.category) {\n        errors.push('Event category is required');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/events.ts\n"));

/***/ })

});